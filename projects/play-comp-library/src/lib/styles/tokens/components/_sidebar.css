/**
 * =========================================================================
 * Play+ Design System: Sidebar Component Tokens
 *
 * Component-specific semantic tokens for sidebar elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for sidebar styling.
 * =========================================================================
 */

:root {
  /* === SIDEBAR CORE TOKENS === */
  --sidebar-border: 1px solid var(--color-border-default);
  --sidebar-background: var(--color-background-primary);
  --sidebar-shadow: var(--global-shadow-sm);
  --sidebar-border-radius: var(--global-border-radius-lg);
  --sidebar-transition: all 0.3s var(--global-motion-easing-standard);

  /* === SIDEBAR SIZE VARIANTS === */
  /* Small Sidebar */
  --sidebar-size-small-width: 200px;
  --sidebar-size-small-collapsed-width: 48px;
  --sidebar-size-small-padding: var(--global-spacing-3);
  --sidebar-size-small-header-height: 48px;
  --sidebar-size-small-footer-height: 48px;

  /* Medium Sidebar */
  --sidebar-size-medium-width: 260px;
  --sidebar-size-medium-collapsed-width: 56px;
  --sidebar-size-medium-padding: var(--global-spacing-4);
  --sidebar-size-medium-header-height: 56px;
  --sidebar-size-medium-footer-height: 56px;

  /* Large Sidebar */
  --sidebar-size-large-width: 320px;
  --sidebar-size-large-collapsed-width: 64px;
  --sidebar-size-large-padding: var(--global-spacing-5);
  --sidebar-size-large-header-height: 64px;
  --sidebar-size-large-footer-height: 64px;

  /* === SIDEBAR MENU ITEM TOKENS === */
  --sidebar-menu-item-padding: var(--global-spacing-3) var(--global-spacing-4);
  --sidebar-menu-item-border-radius: var(--global-border-radius-md);
  --sidebar-menu-item-gap: var(--global-spacing-3);
  --sidebar-menu-item-font: var(--font-body-1);
  --sidebar-menu-item-transition: var(--motion-pattern-fade);

  /* Menu Item States */
  --sidebar-menu-item-background: transparent;
  --sidebar-menu-item-background-hover: var(--color-surface-interactive-hover);
  --sidebar-menu-item-background-active: var(--color-surface-interactive-default);
  --sidebar-menu-item-background-disabled: transparent;

  --sidebar-menu-item-text: var(--color-text-secondary);
  --sidebar-menu-item-text-hover: var(--color-text-primary);
  --sidebar-menu-item-text-active: var(--color-text-on-brand);
  --sidebar-menu-item-text-disabled: var(--color-text-disabled);

  --sidebar-menu-item-icon: var(--color-icon-secondary);
  --sidebar-menu-item-icon-hover: var(--color-icon-primary);
  --sidebar-menu-item-icon-active: var(--color-icon-on-brand);
  --sidebar-menu-item-icon-disabled: var(--color-icon-disabled);

  /* === SIDEBAR SEARCH TOKENS === */
  --sidebar-search-margin: var(--global-spacing-4);
  --sidebar-search-background: var(--color-background-secondary);
  --sidebar-search-border: 1px solid var(--color-border-subtle);
  --sidebar-search-border-radius: var(--global-border-radius-md);

  /* === SIDEBAR USER PROFILE TOKENS === */
  --sidebar-user-profile-padding: var(--global-spacing-4);
  --sidebar-user-profile-border-top: 1px solid var(--color-border-subtle);
  --sidebar-user-profile-background: var(--color-background-secondary);
  --sidebar-user-profile-border-radius: var(--global-border-radius-md);

  /* === SIDEBAR HEADER/FOOTER TOKENS === */
  --sidebar-header-padding: var(--global-spacing-4);
  --sidebar-header-border-bottom: 1px solid var(--color-border-subtle);
  --sidebar-header-background: var(--color-background-primary);

  --sidebar-footer-padding: var(--global-spacing-4);
  --sidebar-footer-border-top: 1px solid var(--color-border-subtle);
  --sidebar-footer-background: var(--color-background-primary);

  /* === SIDEBAR COLLAPSE BUTTON TOKENS === */
  --sidebar-collapse-button-size: 32px;
  --sidebar-collapse-button-background: var(--color-surface-interactive-default);
  --sidebar-collapse-button-background-hover: var(--color-surface-interactive-hover);
  --sidebar-collapse-button-border: 1px solid var(--color-border-default);
  --sidebar-collapse-button-border-radius: var(--global-border-radius-sm);
  --sidebar-collapse-button-shadow: var(--global-shadow-xs);
}
