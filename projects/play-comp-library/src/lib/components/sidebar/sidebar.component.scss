$sidebar-border: var(--sidebar-border);

.ava-sidebar-container {
  display: flex;
  position: relative;
  width: fit-content;

  &.right-positioned {
    flex-direction: row-reverse;
  }

  // === SIZE VARIANTS ===
  &.ava-sidebar-container--small {
    --sidebar-current-padding: var(--sidebar-size-small-padding);
    --sidebar-current-header-height: var(--sidebar-size-small-header-height);
    --sidebar-current-footer-height: var(--sidebar-size-small-footer-height);
  }

  &.ava-sidebar-container--medium {
    --sidebar-current-padding: var(--sidebar-size-medium-padding);
    --sidebar-current-header-height: var(--sidebar-size-medium-header-height);
    --sidebar-current-footer-height: var(--sidebar-size-medium-footer-height);
  }

  &.ava-sidebar-container--large {
    --sidebar-current-padding: var(--sidebar-size-large-padding);
    --sidebar-current-header-height: var(--sidebar-size-large-header-height);
    --sidebar-current-footer-height: var(--sidebar-size-large-footer-height);
  }
}

.ava-sidebar {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: $sidebar-border;

  &.collapsed {
    align-items: center;

    .header-content,
    .sidebar-footer,
    .sidebar-search,
    .sidebar-menu-label,
    .sidebar-menu-badge,
    .sidebar-user-info {
      display: none;
    }

    .header-content-collapsed {
      display: block;
    }

    .sidebar-header {
      justify-content: center;
      align-items: center;
      padding: var(--sidebar-current-padding);
    }

    .sidebar-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: var(--sidebar-current-padding);
    }

    .sidebar-menu-button {
      justify-content: center;
      padding: var(--global-spacing-2);
      min-width: auto;
      width: 40px;
      height: 40px;
    }

    .sidebar-user-profile-button {
      justify-content: center;
      padding: var(--global-spacing-2);
    }
  }

  &.right-positioned {
    border-left: $sidebar-border;
    border-right: none;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  justify-content: center;

 }

.sidebar-content {
  flex: 1 1 auto;
  padding: 1rem;
  overflow-y: auto;
}

// Only show hover area for outside button variants
.ava-sidebar-container.outside-button .hover-area {
  position: absolute;
  top: 0;
  height: 100%;
  width: var(--hover-area-width, 50px); // Use dynamic hover area width
  z-index: 10;
  display: flex;
  align-items: flex-start; // Align button to top
  justify-content: center;
  padding-top: 8px; // Position button at top with small padding
  transition: all 0.3s ease;
  opacity: 0;
  pointer-events: auto; // Always allow hover area to be interactive

  .hover-area-content {
    display: flex;
    width: 100%; // Take full width of hover area
    height: 100%; // Take full height of hover area
    opacity: 0;
    transition: all 0.3s ease;
  }

  // Position the hover area in the white space next to the sidebar
  &.right-positioned {
    right: var(--sidebar-width, 260px); // Position in the white space to the left of sidebar
  }

  &.left-positioned {
    left: var(--sidebar-width, 260px); // Position in the white space to the right of sidebar
  }

  &:hover .hover-area-content {
    opacity: 1;
  }
}

// Show hover area button when hovering over sidebar container OR hover area itself (only for outside button variants)
.ava-sidebar-container.outside-button:hover .hover-area,
.ava-sidebar-container.outside-button .hover-area:hover {
  opacity: 1;
  pointer-events: auto;

  .hover-area-content {
    opacity: 1;
  }
}

// Handle collapsed state positioning
.ava-sidebar-container.outside-button.collapsed .hover-area {
  &.left-positioned {
    left: var(--collapsed-width, 80px); // Position after collapsed sidebar
  }

  &.right-positioned {
    right: var(--collapsed-width, 80px); // Position after collapsed sidebar
  }
}

.sidebar-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ava-sidebar.collapsed .sidebar-header{
  justify-content: center;
}

// === SEARCH SECTION ===
.sidebar-search {
  margin: var(--sidebar-search-margin);

  ava-search-bar {
    width: 100%;
  }
}

// === NAVIGATION MENU ===
.sidebar-navigation {
  flex: 1;
  overflow-y: auto;
  padding: var(--sidebar-current-padding);
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--global-spacing-2);
}

.sidebar-menu-item {
  margin: 0;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.sidebar-menu-button {
  display: flex;
  align-items: center;
  gap: var(--sidebar-menu-item-gap);
  width: 100%;
  padding: var(--sidebar-menu-item-padding);
  border: none;
  border-radius: var(--sidebar-menu-item-border-radius);
  background: var(--sidebar-menu-item-background);
  color: var(--sidebar-menu-item-text);
  font: var(--sidebar-menu-item-font);
  text-align: left;
  cursor: pointer;
  transition: var(--sidebar-menu-item-transition);
  min-height: 44px; // Accessibility: minimum touch target

  &:hover:not(:disabled) {
    background: var(--sidebar-menu-item-background-hover);
    color: var(--sidebar-menu-item-text-hover);

    .sidebar-menu-icon {
      color: var(--sidebar-menu-item-icon-hover);
    }
  }

  &:focus {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
  }

  &:disabled {
    background: var(--sidebar-menu-item-background-disabled);
    color: var(--sidebar-menu-item-text-disabled);
    cursor: not-allowed;

    .sidebar-menu-icon {
      color: var(--sidebar-menu-item-icon-disabled);
    }
  }
}

.sidebar-menu-item.active .sidebar-menu-button {
  background: var(--sidebar-menu-item-background-active);
  color: var(--sidebar-menu-item-text-active);

  .sidebar-menu-icon {
    color: var(--sidebar-menu-item-icon-active);
  }
}

.sidebar-menu-icon {
  color: var(--sidebar-menu-item-icon);
  flex-shrink: 0;
  transition: var(--sidebar-menu-item-transition);
}

.sidebar-menu-label {
  flex: 1;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-menu-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;

  &.sidebar-menu-badge--primary {
    background: var(--color-surface-interactive-default);
    color: var(--color-text-on-brand);
  }

  &.sidebar-menu-badge--secondary {
    background: var(--color-surface-secondary);
    color: var(--color-text-secondary);
  }

  &.sidebar-menu-badge--success {
    background: var(--color-surface-success);
    color: var(--color-text-on-success);
  }

  &.sidebar-menu-badge--warning {
    background: var(--color-surface-warning);
    color: var(--color-text-on-warning);
  }

  &.sidebar-menu-badge--error {
    background: var(--color-surface-error);
    color: var(--color-text-on-error);
  }
}

// === USER PROFILE SECTION ===
.sidebar-user-profile {
  margin-top: auto;
  padding: var(--sidebar-user-profile-padding);
  border-top: var(--sidebar-user-profile-border-top);
  background: var(--sidebar-user-profile-background);
}

.sidebar-user-profile-button {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-3);
  width: 100%;
  padding: var(--global-spacing-3);
  border: none;
  border-radius: var(--sidebar-user-profile-border-radius);
  background: transparent;
  color: var(--color-text-primary);
  text-align: left;
  cursor: pointer;
  transition: var(--motion-pattern-fade);
  min-height: 44px; // Accessibility: minimum touch target

  &:hover {
    background: var(--color-surface-interactive-hover);
  }

  &:focus {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
  }
}

.sidebar-user-avatar {
  flex-shrink: 0;
}

.sidebar-user-info {
  flex: 1;
  min-width: 0; // Allow text truncation
}

.sidebar-user-name {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-user-email {
  font-size: 12px;
  line-height: 1.2;
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.sidebar-user-role {
  font-size: 11px;
  line-height: 1.2;
  color: var(--color-text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.sidebar-user-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;

  &.sidebar-user-status--online {
    background: var(--color-surface-success);
  }

  &.sidebar-user-status--offline {
    background: var(--color-surface-secondary);
  }

  &.sidebar-user-status--away {
    background: var(--color-surface-warning);
  }

  &.sidebar-user-status--busy {
    background: var(--color-surface-error);
  }
}

// === CUSTOM CONTENT ===
.sidebar-custom-content {
  padding: var(--sidebar-current-padding);
}

// === RESPONSIVE DESIGN ===
@media (max-width: 768px) {
  .ava-sidebar-container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;

    &.right-positioned {
      left: auto;
      right: 0;
    }
  }

  .ava-sidebar {
    box-shadow: var(--global-shadow-lg);

    &:not(.collapsed) {
      width: 280px !important;
    }
  }

  // Mobile overlay when sidebar is open
  .ava-sidebar-container:not(.collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

