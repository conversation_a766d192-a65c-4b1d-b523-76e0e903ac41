import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ViewEncapsulation, HostListener } from '@angular/core';
import { ButtonComponent } from '../button/button.component';
import { IconComponent } from '../icon/icon.component';
import { SearchBarComponent } from '../../composite-components/search-bar/search-bar.component';
import { AvatarsComponent } from '../avatars/avatars.component';

// Sidebar size variants following design token patterns
export type SidebarSize = 'small' | 'medium' | 'large';

// Menu item interface for dynamic navigation
export interface SidebarMenuItem {
  id: string;
  label: string;
  icon?: string;
  route?: string;
  active?: boolean;
  disabled?: boolean;
  badge?: {
    count?: number;
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  };
  children?: SidebarMenuItem[];
}

// User profile interface
export interface SidebarUserProfile {
  name: string;
  email?: string;
  avatar?: string;
  role?: string;
  status?: 'online' | 'offline' | 'away' | 'busy';
}

// Search configuration
export interface SidebarSearchConfig {
  enabled: boolean;
  placeholder?: string;
  debounceTime?: number;
  caseSensitive?: boolean;
}

@Component({
  selector: 'ava-sidebar',
  imports: [CommonModule, ButtonComponent, IconComponent, SearchBarComponent, AvatarsComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class SidebarComponent implements OnInit, OnChanges {
  // === CORE SIDEBAR PROPS ===
  @Input() size: SidebarSize = 'medium';
  @Input() width: string = '';
  @Input() collapsedWidth: string = '';
  @Input() height: string = '100vh';
  @Input() hoverAreaWidth: string = '10px';
  @Input() showCollapseButton: boolean = false;
  @Input() buttonVariant: 'inside' | 'outside' = 'inside';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() isCollapsed: boolean = false;
  @Input() position: 'left' | 'right' = 'left';

  // === ENHANCED NAVIGATION PROPS ===
  @Input() menuItems: SidebarMenuItem[] = [];
  @Input() activeItemId: string = '';
  @Input() searchConfig: SidebarSearchConfig = { enabled: false };
  @Input() userProfile: SidebarUserProfile | null = null;
  @Input() showUserProfile: boolean = false;

  // === EVENTS ===
  @Output() collapseToggle = new EventEmitter<boolean>();
  @Output() menuItemClick = new EventEmitter<SidebarMenuItem>();
  @Output() searchQuery = new EventEmitter<string>();
  @Output() userProfileClick = new EventEmitter<SidebarUserProfile>();

  // === INTERNAL STATE ===
  private _isCollapsed = false;
  filteredMenuItems: SidebarMenuItem[] = [];
  currentSearchQuery: string = '';

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this._isCollapsed = this.isCollapsed;
    this.initializeMenuItems();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isCollapsed'] && !changes['isCollapsed'].firstChange) {
      this._isCollapsed = this.isCollapsed;
      this.cdr.markForCheck();
    }

    if (changes['menuItems']) {
      this.initializeMenuItems();
    }

    if (changes['activeItemId']) {
      this.updateActiveStates();
    }
  }

  toggleCollapse(): void {
    this._isCollapsed = !this._isCollapsed;
    this.collapseToggle.emit(this._isCollapsed);
    this.cdr.markForCheck();
  }

  // === SIZE VARIANT CALCULATIONS ===
  get sidebarDimensions() {
    const sizeConfig = {
      small: { width: '200px', collapsedWidth: '48px' },
      medium: { width: '260px', collapsedWidth: '56px' },
      large: { width: '320px', collapsedWidth: '64px' }
    };

    const config = sizeConfig[this.size];
    return {
      width: this.width || config.width,
      collapsedWidth: this.collapsedWidth || config.collapsedWidth
    };
  }

  get sidebarWidth(): string {
    const dimensions = this.sidebarDimensions;
    return this._isCollapsed ? dimensions.collapsedWidth : dimensions.width;
  }

  get collapsed(): boolean {
    return this._isCollapsed;
  }

  get isRightPositioned(): boolean {
    return this.position === 'right';
  }

  get collapseButtonIcon(): string {
    if (this.position === 'right') {
      return this._isCollapsed ? 'chevron-left' : 'chevron-right';
    }
    return this._isCollapsed ? 'chevron-right' : 'chevron-left';
  }

  // === MENU ITEM MANAGEMENT ===
  private initializeMenuItems(): void {
    this.filteredMenuItems = [...this.menuItems];
    this.updateActiveStates();
  }

  private updateActiveStates(): void {
    this.filteredMenuItems = this.filteredMenuItems.map(item => ({
      ...item,
      active: item.id === this.activeItemId
    }));
  }

  onMenuItemClick(item: SidebarMenuItem): void {
    if (item.disabled) return;

    this.activeItemId = item.id;
    this.updateActiveStates();
    this.menuItemClick.emit(item);
    this.cdr.markForCheck();
  }

  // === SEARCH FUNCTIONALITY ===
  onSearchQueryChange(query: string): void {
    this.currentSearchQuery = query;
    this.filterMenuItems(query);
    this.searchQuery.emit(query);
  }

  private filterMenuItems(query: string): void {
    if (!query.trim()) {
      this.filteredMenuItems = [...this.menuItems];
      return;
    }

    const searchTerm = this.searchConfig.caseSensitive ? query : query.toLowerCase();

    this.filteredMenuItems = this.menuItems.filter(item => {
      const itemLabel = this.searchConfig.caseSensitive ? item.label : item.label.toLowerCase();
      return itemLabel.includes(searchTerm);
    });

    this.cdr.markForCheck();
  }

  // === USER PROFILE ===
  onUserProfileClick(): void {
    if (this.userProfile) {
      this.userProfileClick.emit(this.userProfile);
    }
  }

  // === KEYBOARD NAVIGATION ===
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.menuItems.length) return;

    const currentIndex = this.filteredMenuItems.findIndex(item => item.active);
    let newIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        newIndex = currentIndex < this.filteredMenuItems.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'ArrowUp':
        event.preventDefault();
        newIndex = currentIndex > 0 ? currentIndex - 1 : this.filteredMenuItems.length - 1;
        break;
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (currentIndex >= 0) {
          this.onMenuItemClick(this.filteredMenuItems[currentIndex]);
        }
        break;
      case 'Escape':
        if (!this.collapsed) {
          this.toggleCollapse();
        }
        break;
      default:
        return;
    }

    if (newIndex !== currentIndex && newIndex >= 0) {
      const newActiveItem = this.filteredMenuItems[newIndex];
      this.activeItemId = newActiveItem.id;
      this.updateActiveStates();
      this.cdr.markForCheck();
    }
  }

  // === UTILITY FUNCTIONS ===
  trackByItemId(index: number, item: SidebarMenuItem): string {
    return item.id;
  }
}
