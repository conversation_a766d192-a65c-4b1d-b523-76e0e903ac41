import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SidebarComponent, SidebarMenuItem, SidebarUserProfile, SidebarSearchConfig } from './sidebar.component';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

@Component({
  selector: 'ava-button',
  template: '<button (userClick)="userClick.emit($event)"><ng-content></ng-content></button>',
  standalone: true
})
class MockButtonComponent {
  @Input() iconName: string = '';
  @Input() iconPosition: string = '';
  @Input() size: string = '';
  @Input() variant: string = '';
  @Output() userClick = new EventEmitter<Event>();
}

@Component({
  selector: 'ava-icon',
  template: '<span class="mock-icon">{{ iconName }}</span>',
  standalone: true
})
class MockIconComponent {
  @Input() iconName: string = '';
  @Input() iconSize: number = 24;
  @Input() iconColor: string = '';
}

@Component({
  selector: 'ava-search-bar',
  template: '<input (input)="onInput($event)" [placeholder]="placeholder">',
  standalone: true
})
class MockSearchBarComponent {
  @Input() label: string = '';
  @Input() placeholder: string = '';
  @Input() size: string = '';
  @Output() searchClick = new EventEmitter<string>();
  @Output() textboxChange = new EventEmitter<Event>();

  onInput(event: Event) {
    this.textboxChange.emit(event);
  }
}

@Component({
  selector: 'ava-avatars',
  template: '<div class="mock-avatar">{{ statusText }}</div>',
  standalone: true
})
class MockAvatarsComponent {
  @Input() size: string = '';
  @Input() imageUrl: string = '';
  @Input() statusText: string = '';
  @Input() additionalText: string = '';
}

describe('SidebarComponent', () => {
  let component: SidebarComponent;
  let fixture: ComponentFixture<SidebarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        SidebarComponent,
        MockButtonComponent,
        MockIconComponent,
        MockSearchBarComponent,
        MockAvatarsComponent
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SidebarComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should calculate sidebarWidth based on collapse state', () => {
    component.isCollapsed = false;
    component.width = '300px';
    component.collapsedWidth = '50px';
    component.ngOnInit();
    expect(component.sidebarWidth).toBe('300px');
    component.toggleCollapse();
    expect(component.sidebarWidth).toBe('50px');
  });

  it('should render header, content, and footer when visible', () => {
    component.showHeader = true;
    component.showFooter = true;
    fixture.detectChanges();
    const header = fixture.debugElement.query(By.css('.sidebar-header'));
    const content = fixture.debugElement.query(By.css('.sidebar-content'));
    const footer = fixture.debugElement.query(By.css('.sidebar-footer'));
    expect(header).toBeTruthy();
    expect(content).toBeTruthy();
    expect(footer).toBeTruthy();
  });

  it('should not render header and footer if disabled', () => {
    component.showHeader = false;
    component.showFooter = false;
    fixture.detectChanges();
    const header = fixture.debugElement.query(By.css('.sidebar-header'));
    const footer = fixture.debugElement.query(By.css('.sidebar-footer'));
    expect(header).toBeFalsy();
    expect(footer).toBeFalsy();
  });

  describe('Position functionality', () => {
    it('should default to left position', () => {
      expect(component.position).toBe('left');
      expect(component.isRightPositioned).toBe(false);
    });

    it('should detect right position correctly', () => {
      component.position = 'right';
      expect(component.isRightPositioned).toBe(true);
    });

    it('should apply right-positioned class when position is right', () => {
      component.position = 'right';
      fixture.detectChanges();
      const container = fixture.debugElement.query(By.css('.ava-sidebar-container'));
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(container.nativeElement.classList.contains('right-positioned')).toBe(true);
      expect(sidebar.nativeElement.classList.contains('right-positioned')).toBe(true);
    });

    it('should not apply right-positioned class when position is left', () => {
      component.position = 'left';
      fixture.detectChanges();
      const container = fixture.debugElement.query(By.css('.ava-sidebar-container'));
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(container.nativeElement.classList.contains('right-positioned')).toBe(false);
      expect(sidebar.nativeElement.classList.contains('right-positioned')).toBe(false);
    });

    it('should return correct collapse button icon for left position', () => {
      component.position = 'left';
      component.ngOnInit();

      // When not collapsed, should show ArrowLeft
      expect(component.collapseButtonIcon).toBe('ArrowLeft');

      // When collapsed, should show ArrowRight
      component.toggleCollapse();
      expect(component.collapseButtonIcon).toBe('ArrowRight');
    });

    it('should return correct collapse button icon for right position', () => {
      component.position = 'right';
      component.ngOnInit();

      // When not collapsed, should show ArrowRight
      expect(component.collapseButtonIcon).toBe('ArrowRight');

      // When collapsed, should show ArrowLeft
      component.toggleCollapse();
      expect(component.collapseButtonIcon).toBe('ArrowLeft');
    });
  });

  describe('Collapse functionality', () => {
    it('should emit collapseToggle event when toggled', () => {
      spyOn(component.collapseToggle, 'emit');
      component.toggleCollapse();
      expect(component.collapseToggle.emit).toHaveBeenCalledWith(true);

      component.toggleCollapse();
      expect(component.collapseToggle.emit).toHaveBeenCalledWith(false);
    });

    it('should update collapsed state when isCollapsed input changes', () => {
      component.isCollapsed = true;
      component.ngOnChanges({
        isCollapsed: {
          currentValue: true,
          previousValue: false,
          firstChange: false,
          isFirstChange: () => false
        }
      });
      expect(component.collapsed).toBe(true);
    });

    it('should apply collapsed class when sidebar is collapsed', () => {
      component.toggleCollapse();
      fixture.detectChanges();
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(sidebar.nativeElement.classList.contains('collapsed')).toBe(true);
    });
  });

  describe('Size Variants', () => {
    it('should apply correct size classes', () => {
      component.size = 'large';
      fixture.detectChanges();

      const container = fixture.debugElement.query(By.css('.ava-sidebar-container'));
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));

      expect(container.nativeElement.classList.contains('ava-sidebar-container--large')).toBe(true);
      expect(sidebar.nativeElement.classList.contains('ava-sidebar--large')).toBe(true);
    });

    it('should calculate correct dimensions for different sizes', () => {
      component.size = 'small';
      const smallDimensions = component.sidebarDimensions;
      expect(smallDimensions.width).toBe('200px');
      expect(smallDimensions.collapsedWidth).toBe('48px');

      component.size = 'medium';
      const mediumDimensions = component.sidebarDimensions;
      expect(mediumDimensions.width).toBe('260px');
      expect(mediumDimensions.collapsedWidth).toBe('56px');

      component.size = 'large';
      const largeDimensions = component.sidebarDimensions;
      expect(largeDimensions.width).toBe('320px');
      expect(largeDimensions.collapsedWidth).toBe('64px');
    });

    it('should use custom width when provided', () => {
      component.size = 'medium';
      component.width = '300px';
      component.collapsedWidth = '70px';

      const dimensions = component.sidebarDimensions;
      expect(dimensions.width).toBe('300px');
      expect(dimensions.collapsedWidth).toBe('70px');
    });
  });

  describe('Menu Items', () => {
    const mockMenuItems: SidebarMenuItem[] = [
      { id: '1', label: 'Dashboard', icon: 'dashboard', active: false },
      { id: '2', label: 'Users', icon: 'users', active: true },
      { id: '3', label: 'Settings', icon: 'settings', active: false, disabled: true }
    ];

    beforeEach(() => {
      component.menuItems = mockMenuItems;
      component.ngOnInit();
    });

    it('should initialize filtered menu items', () => {
      expect(component.filteredMenuItems).toEqual(mockMenuItems);
    });

    it('should render menu items', () => {
      fixture.detectChanges();
      const menuItems = fixture.debugElement.queryAll(By.css('.sidebar-menu-item'));
      expect(menuItems.length).toBe(3);
    });

    it('should handle menu item click', () => {
      spyOn(component.menuItemClick, 'emit');
      const testItem = mockMenuItems[0];

      component.onMenuItemClick(testItem);

      expect(component.activeItemId).toBe('1');
      expect(component.menuItemClick.emit).toHaveBeenCalledWith(testItem);
    });

    it('should not handle click for disabled items', () => {
      spyOn(component.menuItemClick, 'emit');
      const disabledItem = mockMenuItems[2];

      component.onMenuItemClick(disabledItem);

      expect(component.menuItemClick.emit).not.toHaveBeenCalled();
    });

    it('should update active states correctly', () => {
      component.activeItemId = '3';
      component.updateActiveStates();

      expect(component.filteredMenuItems[0].active).toBe(false);
      expect(component.filteredMenuItems[1].active).toBe(false);
      expect(component.filteredMenuItems[2].active).toBe(true);
    });

    it('should track items by id', () => {
      const item = mockMenuItems[0];
      const trackResult = component.trackByItemId(0, item);
      expect(trackResult).toBe('1');
    });
  });

  describe('Search Functionality', () => {
    const mockMenuItems: SidebarMenuItem[] = [
      { id: '1', label: 'Dashboard', icon: 'dashboard' },
      { id: '2', label: 'User Management', icon: 'users' },
      { id: '3', label: 'Settings', icon: 'settings' }
    ];

    beforeEach(() => {
      component.menuItems = mockMenuItems;
      component.searchConfig = { enabled: true, caseSensitive: false };
      component.ngOnInit();
    });

    it('should filter menu items based on search query', () => {
      component.onSearchQueryChange('user');

      expect(component.filteredMenuItems.length).toBe(1);
      expect(component.filteredMenuItems[0].label).toBe('User Management');
    });

    it('should be case insensitive by default', () => {
      component.onSearchQueryChange('USER');

      expect(component.filteredMenuItems.length).toBe(1);
      expect(component.filteredMenuItems[0].label).toBe('User Management');
    });

    it('should respect case sensitive setting', () => {
      component.searchConfig.caseSensitive = true;
      component.onSearchQueryChange('USER');

      expect(component.filteredMenuItems.length).toBe(0);
    });

    it('should show all items when search is empty', () => {
      component.onSearchQueryChange('user');
      expect(component.filteredMenuItems.length).toBe(1);

      component.onSearchQueryChange('');
      expect(component.filteredMenuItems.length).toBe(3);
    });

    it('should emit search query', () => {
      spyOn(component.searchQuery, 'emit');
      component.onSearchQueryChange('test');

      expect(component.searchQuery.emit).toHaveBeenCalledWith('test');
    });

    it('should render search bar when enabled and not collapsed', () => {
      component.searchConfig.enabled = true;
      component.isCollapsed = false;
      fixture.detectChanges();

      const searchBar = fixture.debugElement.query(By.css('.sidebar-search'));
      expect(searchBar).toBeTruthy();
    });

    it('should not render search bar when collapsed', () => {
      component.searchConfig.enabled = true;
      component.isCollapsed = true;
      fixture.detectChanges();

      const searchBar = fixture.debugElement.query(By.css('.sidebar-search'));
      expect(searchBar).toBeFalsy();
    });
  });

  describe('User Profile', () => {
    const mockUserProfile: SidebarUserProfile = {
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'avatar.jpg',
      role: 'Admin',
      status: 'online'
    };

    beforeEach(() => {
      component.userProfile = mockUserProfile;
      component.showUserProfile = true;
    });

    it('should render user profile when enabled', () => {
      fixture.detectChanges();

      const userProfile = fixture.debugElement.query(By.css('.sidebar-user-profile'));
      expect(userProfile).toBeTruthy();
    });

    it('should not render user profile when disabled', () => {
      component.showUserProfile = false;
      fixture.detectChanges();

      const userProfile = fixture.debugElement.query(By.css('.sidebar-user-profile'));
      expect(userProfile).toBeFalsy();
    });

    it('should handle user profile click', () => {
      spyOn(component.userProfileClick, 'emit');

      component.onUserProfileClick();

      expect(component.userProfileClick.emit).toHaveBeenCalledWith(mockUserProfile);
    });

    it('should not emit when user profile is null', () => {
      spyOn(component.userProfileClick, 'emit');
      component.userProfile = null;

      component.onUserProfileClick();

      expect(component.userProfileClick.emit).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Navigation', () => {
    const mockMenuItems: SidebarMenuItem[] = [
      { id: '1', label: 'Dashboard', icon: 'dashboard', active: true },
      { id: '2', label: 'Users', icon: 'users', active: false },
      { id: '3', label: 'Settings', icon: 'settings', active: false }
    ];

    beforeEach(() => {
      component.menuItems = mockMenuItems;
      component.activeItemId = '1';
      component.ngOnInit();
    });

    it('should navigate down with ArrowDown key', () => {
      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      spyOn(event, 'preventDefault');

      component.onKeyDown(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.activeItemId).toBe('2');
    });

    it('should navigate up with ArrowUp key', () => {
      component.activeItemId = '2';
      component.updateActiveStates();

      const event = new KeyboardEvent('keydown', { key: 'ArrowUp' });
      spyOn(event, 'preventDefault');

      component.onKeyDown(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.activeItemId).toBe('1');
    });

    it('should wrap to first item when navigating down from last item', () => {
      component.activeItemId = '3';
      component.updateActiveStates();

      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      component.onKeyDown(event);

      expect(component.activeItemId).toBe('1');
    });

    it('should wrap to last item when navigating up from first item', () => {
      const event = new KeyboardEvent('keydown', { key: 'ArrowUp' });
      component.onKeyDown(event);

      expect(component.activeItemId).toBe('3');
    });

    it('should activate item with Enter key', () => {
      spyOn(component, 'onMenuItemClick');

      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      spyOn(event, 'preventDefault');

      component.onKeyDown(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.onMenuItemClick).toHaveBeenCalledWith(mockMenuItems[0]);
    });

    it('should activate item with Space key', () => {
      spyOn(component, 'onMenuItemClick');

      const event = new KeyboardEvent('keydown', { key: ' ' });
      spyOn(event, 'preventDefault');

      component.onKeyDown(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.onMenuItemClick).toHaveBeenCalledWith(mockMenuItems[0]);
    });

    it('should collapse sidebar with Escape key when expanded', () => {
      component.isCollapsed = false;
      spyOn(component, 'toggleCollapse');

      const event = new KeyboardEvent('keydown', { key: 'Escape' });
      component.onKeyDown(event);

      expect(component.toggleCollapse).toHaveBeenCalled();
    });

    it('should not handle keyboard events when no menu items', () => {
      component.menuItems = [];
      component.filteredMenuItems = [];

      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      spyOn(event, 'preventDefault');

      component.onKeyDown(event);

      expect(event.preventDefault).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.menuItems = [
        { id: '1', label: 'Dashboard', icon: 'dashboard', active: true }
      ];
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should have proper ARIA attributes on sidebar', () => {
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));

      expect(sidebar.nativeElement.getAttribute('role')).toBe('navigation');
      expect(sidebar.nativeElement.getAttribute('aria-label')).toBe('Sidebar navigation');
      expect(sidebar.nativeElement.getAttribute('aria-expanded')).toBe('true');
    });

    it('should update aria-expanded when collapsed', () => {
      component.toggleCollapse();
      fixture.detectChanges();

      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(sidebar.nativeElement.getAttribute('aria-expanded')).toBe('false');
    });

    it('should have proper ARIA attributes on menu items', () => {
      const menuButton = fixture.debugElement.query(By.css('.sidebar-menu-button'));

      expect(menuButton.nativeElement.getAttribute('role')).toBe('menuitem');
      expect(menuButton.nativeElement.getAttribute('aria-label')).toBe('Dashboard');
      expect(menuButton.nativeElement.getAttribute('aria-current')).toBe('page');
      expect(menuButton.nativeElement.getAttribute('tabindex')).toBe('0');
    });

    it('should have proper ARIA labels on collapse button', () => {
      component.showCollapseButton = true;
      component.buttonVariant = 'inside';
      fixture.detectChanges();

      const button = fixture.debugElement.query(By.css('ava-button'));
      expect(button.nativeElement.getAttribute('aria-label')).toBe('Collapse sidebar');
    });
  });

});
