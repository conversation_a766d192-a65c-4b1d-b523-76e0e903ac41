<div class="ava-sidebar-container"
     [class.right-positioned]="isRightPositioned"
     [class.outside-button]="buttonVariant === 'outside'"
     [class]="'ava-sidebar-container--' + size"
     [style.--sidebar-width]="sidebarWidth"
     [style.--hover-area-width]="hoverAreaWidth">
  <!-- Main Sidebar -->

  <div
    class="ava-sidebar"
    [class]="'ava-sidebar--' + size"
    [style.width]="sidebarWidth"
    [style.height]="height"
    [class.collapsed]="collapsed"
    [class.right-positioned]="isRightPositioned"
    role="navigation"
    [attr.aria-label]="'Sidebar navigation'"
    [attr.aria-expanded]="!collapsed"
  >
    <!-- Header Section -->
    <div class="sidebar-header" *ngIf="showHeader">
      <!-- Logo/Brand Section -->
      <div class="header-content" *ngIf="!collapsed">
        <ng-content select="[slot=header]"></ng-content>
      </div>

      <!-- Collapsed Logo -->
      <div class="header-content-collapsed" *ngIf="collapsed">
        <ng-content select="[slot=header-collapsed]"></ng-content>
      </div>

      <!-- Inside Button Variant -->
      <div
        class="header-controls"
        *ngIf="showCollapseButton && buttonVariant === 'inside'"
      >
        <ava-button
          [iconName]="collapseButtonIcon"
          iconPosition="only"
          size="small"
          (userClick)="toggleCollapse()"
          variant="primary"
          [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
        ></ava-button>
      </div>
    </div>

    <!-- Search Section -->
    <div class="sidebar-search" *ngIf="searchConfig.enabled && !collapsed">
      <ava-search-bar
        [label]="''"
        [placeholder]="searchConfig.placeholder || 'Search...'"
        [size]="size === 'small' ? 'xs' : size === 'large' ? 'lg' : 'md'"
        (searchClick)="onSearchQueryChange($event)"
        (textboxChange)="onSearchQueryChange($event.target.value)"
      ></ava-search-bar>
    </div>

    <!-- Main Content Section -->
    <div class="sidebar-content">
      <!-- Dynamic Menu Items -->
      <nav class="sidebar-navigation" *ngIf="menuItems.length > 0" role="navigation">
        <ul class="sidebar-menu" role="menubar">
          <li
            *ngFor="let item of filteredMenuItems; trackBy: trackByItemId"
            class="sidebar-menu-item"
            [class.active]="item.active"
            [class.disabled]="item.disabled"
            role="none"
          >
            <button
              class="sidebar-menu-button"
              [disabled]="item.disabled"
              (click)="onMenuItemClick(item)"
              role="menuitem"
              [attr.aria-label]="item.label"
              [attr.aria-current]="item.active ? 'page' : null"
              tabindex="0"
            >
              <!-- Menu Item Icon -->
              <ava-icon
                *ngIf="item.icon"
                [iconName]="item.icon"
                [iconSize]="size === 'small' ? 16 : size === 'large' ? 24 : 20"
                class="sidebar-menu-icon"
              ></ava-icon>

              <!-- Menu Item Label -->
              <span class="sidebar-menu-label" *ngIf="!collapsed">
                {{ item.label }}
              </span>

              <!-- Badge -->
              <span
                *ngIf="item.badge && item.badge.count && !collapsed"
                class="sidebar-menu-badge"
                [class]="'sidebar-menu-badge--' + (item.badge.variant || 'primary')"
              >
                {{ item.badge.count }}
              </span>
            </button>
          </li>
        </ul>
      </nav>

      <!-- Content Projection for Custom Content -->
      <div class="sidebar-custom-content">
        <ng-content select="[slot=content]"></ng-content>
      </div>
    </div>

    <!-- User Profile Section -->
    <div class="sidebar-user-profile" *ngIf="showUserProfile && userProfile">
      <button
        class="sidebar-user-profile-button"
        (click)="onUserProfileClick()"
        [attr.aria-label]="'User profile: ' + userProfile.name"
        tabindex="0"
      >
        <!-- User Avatar -->
        <ava-avatars
          [size]="size === 'small' ? 'small' : size === 'large' ? 'large' : 'medium'"
          [imageUrl]="userProfile.avatar || ''"
          [statusText]="collapsed ? '' : userProfile.name"
          [additionalText]="collapsed ? '' : userProfile.email"
          class="sidebar-user-avatar"
        ></ava-avatars>

        <!-- User Info (when expanded) -->
        <div class="sidebar-user-info" *ngIf="!collapsed">
          <div class="sidebar-user-name">{{ userProfile.name }}</div>
          <div class="sidebar-user-email" *ngIf="userProfile.email">{{ userProfile.email }}</div>
          <div class="sidebar-user-role" *ngIf="userProfile.role">{{ userProfile.role }}</div>
        </div>

        <!-- Status Indicator -->
        <div
          *ngIf="userProfile.status && !collapsed"
          class="sidebar-user-status"
          [class]="'sidebar-user-status--' + userProfile.status"
          [attr.aria-label]="'Status: ' + userProfile.status"
        ></div>
      </button>
    </div>

    <!-- Footer Section -->
    <div class="sidebar-footer" *ngIf="showFooter">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
  </div>

  <!-- Outside Button Variant - Hover Area -->
  <div
    class="hover-area"
    [class.right-positioned]="isRightPositioned"
    [class.left-positioned]="!isRightPositioned"
    *ngIf="showCollapseButton && buttonVariant === 'outside'"
  >
    <div class="hover-area-content">
      <ava-button
        variant="primary"
        [iconName]="collapseButtonIcon"
        iconPosition="only"
        size="small"
        (userClick)="toggleCollapse()"
        [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'"
      ></ava-button>
    </div>
  </div>
</div>
